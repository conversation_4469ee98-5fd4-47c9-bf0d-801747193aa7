feat: Auto-clear vouchers on cart quantity changes

- Clear all selected vouchers when cart total changes (quantity/item changes)
- Show user-friendly notification asking to reselect appropriate vouchers
- Simplify logic by avoiding complex validation during cart changes
- Voucher validation occurs only when user selects vouchers

Implementation:
- Track totalPrice changes in CheckoutPage with useEffect
- Add onCartChange callback to OrderSummary component
- Clear all selectedVouchers when cart total changes
- Show info toast: "Voucher đã được bỏ chọn do thay đổi giỏ hàng. Vui lòng chọn lại voucher phù hợp."
- Let VoucherSelect component handle validation when user reselects

Benefits:
- Simple and predictable behavior for users
- Avoids confusion from partial voucher clearing
- Clear user guidance to reselect appropriate vouchers
- Validation happens at the right time (during voucher selection)

Files modified:
- pages/CheckoutPage.tsx: Add voucher clearing logic and cart change tracking
- modules/checkout/components/OrderSummary.tsx: Add cart change callback

Resolves voucher handling on cart modifications with simplified approach.
