import { axiosInstance } from '../config';

export const DEFAULT_VOUCHER_IMAGE = '/images/image.png';

export interface Voucher {
  id: number;
  code: string;
  description: string;
  value: number;
  is_percentage: boolean;
  is_active: boolean;
  min_purchase_amount: number;
  usage_limit: number;
  usage_count: number;
  valid_from: string;
  valid_until: string;
  is_valid: boolean;
  image?: string;
  image_url?: string; // Added for default image handling
}

export interface VoucherResponse {
  success: boolean;
  vouchers: Voucher[];
  message?: string;
}

export const calculateVoucherDiscount = (voucher: Voucher, totalPrice: number): number => {
  if (voucher.is_percentage) {
    return Math.floor((voucher.value * totalPrice) / 100);
  }
  return voucher.value;
};

export const formatVoucherValue = (voucher: Voucher): string => {
  if (voucher.value == 0 && !voucher.is_percentage) {
    return 'Freeship';
  }
  if (voucher.is_percentage) {
    return `<PERSON><PERSON>ảm ${voucher.value}%`;
  }
  return `Giảm ${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(voucher.value)}`;
};

/**
 * Check if a voucher is currently valid
 * @param voucher - The voucher to validate
 * @returns boolean indicating if the voucher is valid
 */
export const isVoucherValid = (voucher: Voucher): boolean => {
  // Use the is_valid field from backend if available
  if (typeof voucher.is_valid === 'boolean') {
    return voucher.is_valid;
  }

  // Fallback to manual validation if is_valid field is not available
  const now = new Date();
  const validFrom = new Date(voucher.valid_from);
  const validUntil = new Date(voucher.valid_until);

  return (
    voucher.is_active &&
    voucher.usage_count < voucher.usage_limit &&
    now >= validFrom &&
    now <= validUntil
  );
};

/**
 * Check if a voucher is expired
 * @param voucher - The voucher to check
 * @returns boolean indicating if the voucher is expired
 */
export const isVoucherExpired = (voucher: Voucher): boolean => {
  const now = new Date();
  const validUntil = new Date(voucher.valid_until);
  return now > validUntil;
};

/**
 * Check if a voucher has reached its usage limit
 * @param voucher - The voucher to check
 * @returns boolean indicating if the voucher is full
 */
export const isVoucherFull = (voucher: Voucher): boolean => {
  return voucher.usage_count >= voucher.usage_limit;
};

/**
 * Filter vouchers to only include valid ones
 * @param vouchers - Array of vouchers to filter
 * @returns Array of valid vouchers only
 */
export const filterValidVouchers = (vouchers: Voucher[]): Voucher[] => {
  return vouchers.filter(isVoucherValid);
};

export const voucherApi = {
  getAvailableVouchers: async (): Promise<VoucherResponse> => {
    try {
      // Try the authenticated endpoint first
      try {
        const response = await axiosInstance.get('/api/promotions');
        console.log('Available vouchers:', response.data);

        // Process vouchers: add default image and filter valid ones
        const allVouchers = response.data.results.map((voucher: Voucher) => ({
          ...voucher,
          image_url: voucher.image_url || DEFAULT_VOUCHER_IMAGE
        }));

        // Filter only valid vouchers
        const validVouchers = filterValidVouchers(allVouchers);

        return { success: true, vouchers: validVouchers };
      } catch (authError) {
        // If authentication fails, use the public endpoint
        const publicResponse = await axiosInstance.get('/api/promotions/public');

        // Process vouchers: add default image and filter valid ones
        const allVouchers = publicResponse.data.results.map((voucher: Voucher) => ({
          ...voucher,
          image_url: voucher.image_url || DEFAULT_VOUCHER_IMAGE
        }));

        // Filter only valid vouchers
        const validVouchers = filterValidVouchers(allVouchers);

        return { success: true, vouchers: validVouchers };
      }
    } catch (error) {
      console.error('Error fetching vouchers:', error);
      return { success: false, vouchers: [], message: 'Failed to fetch vouchers' };
    }
  },
};
