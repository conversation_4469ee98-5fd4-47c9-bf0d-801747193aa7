import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';
import { Search } from 'lucide-react';
import {
  voucherApi,
  Voucher,
  DEFAULT_VOUCHER_IMAGE,
  formatVoucherValue
} from '@/lib/api/voucher/voucher';

interface VoucherSelectProps {
  onVouchersChange: (vouchers: Voucher[]) => void;
  totalPrice: number;
  selectedVouchers: Voucher[];
  cityCode?: string; // Add cityCode prop to check if user is in HCMC
}

export function VoucherSelect({ onVouchersChange, totalPrice, selectedVouchers, cityCode }: VoucherSelectProps) {
  const [vouchers, setVouchers] = useState<Voucher[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchVouchers();

    // Close dropdown when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const fetchVouchers = async () => {
    try {
      const response = await voucherApi.getAvailableVouchers();
      if (response.success) {
        // The API now returns only valid vouchers, so no need for additional filtering
        setVouchers(response.vouchers);
      } else {
        toast.error('Không thể tải danh sách voucher');
      }
    } catch (error) {
      toast.error('Đã có lỗi xảy ra khi tải voucher');
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to check if a voucher is a freeship voucher
  const isFreeshipVoucher = (voucher: Voucher): boolean => {
    return Number(voucher.value).toFixed(2) === '0.00' && !voucher.is_percentage;
  };

  // Helper function to check if the delivery address is in Ho Chi Minh City
  const isInHoChiMinhCity = (): boolean => {
    // HCMC city code is '79'
    return cityCode === '79';
  };

  const isVoucherDisabled = (voucher: Voucher) => {
    return voucher.min_purchase_amount > totalPrice;
  };

  const handleVoucherSelect = (voucher: Voucher) => {
    if (isVoucherDisabled(voucher)) {
      toast.warning(`Đơn hàng chưa đạt giá trị tối thiểu ${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(voucher.min_purchase_amount)}`);
      return;
    }

    // Check if it's a freeship voucher and the delivery address is not in HCMC
    if (isFreeshipVoucher(voucher) && !isInHoChiMinhCity()) {
      toast.warning("Voucher Freeship chỉ áp dụng trong khu vực Thành phố Hồ Chí Minh");
      return;
    }

    // Replace the current voucher with the new one instead of adding to array
    const newVouchers = [voucher];
    onVouchersChange(newVouchers);

    if (selectedVouchers.length > 0) {
      toast.success(`Đã thay đổi sang voucher ${voucher.code}`);
    } else {
      toast.success(`Đã áp dụng voucher ${voucher.code}`);
    }
    setIsOpen(false);
  };

  const removeVoucher = (voucherId: number) => {
    const newVouchers = selectedVouchers.filter(v => v.id !== voucherId);
    onVouchersChange(newVouchers);
    toast.info('Đã xóa voucher');
  };

  const filteredVouchers = vouchers
    .filter(voucher => voucher.code.toLowerCase().includes(searchTerm.toLowerCase()))
    .sort((a, b) => a.code.localeCompare(b.code));

  return (
    <div className="bg-white p-6 rounded-lg border mb-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium">Voucher giảm giá</h2>
        {/* <span className="text-xs text-gray-500 italic">(Chỉ được chọn 1 voucher)</span> */}
      </div>

      <div className="relative" ref={dropdownRef}>
        <div
          onClick={() => setIsOpen(!isOpen)}
          className="w-full px-4 py-2.5 border rounded-lg cursor-pointer hover:border-blue-500 focus:outline-none flex items-center justify-between bg-white"
        >
          <span className="text-gray-700">
            {selectedVouchers.length > 0
              ? `Đã chọn voucher ${selectedVouchers[0].code}`
              : 'Chọn voucher'}
          </span>
          <span className="text-gray-400 transition-transform duration-200" style={{ transform: isOpen ? 'rotate(180deg)' : 'none' }}>
            ▼
          </span>
        </div>

        {isOpen && (
          <>
            <div className="fixed inset-0 bg-black/5 z-10" onClick={() => setIsOpen(false)} />
            <div
              className="absolute w-full mt-1 bg-white border rounded-lg shadow-lg overflow-hidden z-20"
              style={{ top: "calc(100% + 4px)" }}
            >
              <div className="bg-white p-3 border-b">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Nhập mã voucher..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <Search className="absolute left-3 top-2.5 text-gray-400" size={20} />
                </div>
              </div>

              <div
                ref={listRef}
                className="overflow-y-auto select-none scrollbar-thin scrollbar-thumb-gray-400/60 hover:scrollbar-thumb-gray-400 scrollbar-track-transparent"
                style={{ maxHeight: 'calc(3 * 84px)' }}
              >
                {isLoading ? (
                  <div className="p-4 text-center text-gray-500">Đang tải...</div>
                ) : filteredVouchers.length > 0 ? (
                  <>
                    {filteredVouchers.map(voucher => (
                      <div
                        key={voucher.id}
                        onClick={() => handleVoucherSelect(voucher)}
                        className="flex items-center px-4 py-3 h-[84px] border-b last:border-b-0 transition-colors duration-150 bg-white cursor-pointer hover:bg-blue-50/70 active:bg-blue-100/70"
                      >
                        <div className="w-14 h-14 flex-shrink-0 bg-gray-100 rounded-lg mr-3 overflow-hidden">
                          <img
                            src={voucher.image_url || DEFAULT_VOUCHER_IMAGE}
                            alt={voucher.code}
                            className="w-full h-full object-cover"
                          />
                        </div>

                        <div className="flex-1">
                          <div className="font-medium text-blue-600">{voucher.code}</div>
                          <div className="text-sm text-gray-500 line-clamp-1 mt-0.5">{voucher.description}</div>
                          <div className="flex items-center gap-2 mt-1.5">
                      <span className="text-sm text-green-600 font-medium">{formatVoucherValue(voucher)}</span>
                      {voucher.min_purchase_amount > 0 && voucher.value !== 0 && (
                        <span className="text-xs text-gray-500">
                          | Đơn tối thiểu {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' })
                            .format(voucher.min_purchase_amount)}
                        </span>
                      )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </>
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    {searchTerm ? 'Không tìm thấy voucher phù hợp' : 'Không có voucher khả dụng'}
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>

      {selectedVouchers.length > 0 && (
        <div className="mt-4 space-y-2">
          {selectedVouchers.map(voucher => (
            <div key={voucher.id} className="p-3 bg-blue-50 rounded-lg">
              <div className="flex justify-between items-start">
                <div className="flex items-start">
                  <div className="w-14 h-14 flex-shrink-0 bg-gray-100 rounded-lg mr-3 overflow-hidden">
                    <img
                      src={voucher.image_url || DEFAULT_VOUCHER_IMAGE}
                      alt={voucher.code}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  <div>
                    <p className="font-medium text-blue-600 mb-0.5">{voucher.code}</p>
                    <p className="text-sm text-gray-600 line-clamp-1">{voucher.description}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-sm text-green-600 font-medium">{formatVoucherValue(voucher)}</span>
                      {voucher.min_purchase_amount > 0 && (
                        <span className="text-xs text-gray-500">
                          | Đơn tối thiểu {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' })
                            .format(voucher.min_purchase_amount)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <button
                  type="button"
                  onClick={() => removeVoucher(voucher.id)}
                  className="text-red-500 hover:text-red-700 p-1"
                >
                  Xóa
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
