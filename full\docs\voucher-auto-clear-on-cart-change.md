# Fix: Auto Clear Voucher on Cart Changes

## Mô tả yêu cầu
<PERSON> khách hàng thay đổi số lượng sản phẩm trong checkout page (làm thay đổi tổng tiền), hệ thống sẽ tự động kiểm tra và loại bỏ voucher đã chọn nếu không còn đáp ứng điều kiện áp dụng (min_purchase_amount).

## Vấn đề cần giải quyết
- User thay đổi quantity → totalPrice thay đổi
- Voucher đã chọn có thể không còn hợp lệ (totalPrice < min_purchase_amount)
- Cần tự động clear voucher để tránh sai sót trong logic tính toán

## Giải pháp thực hiện

### 1. Tracking Total Price Changes

**File: `full/frontend/src/pages/CheckoutPage.tsx`**

#### a) Thêm state tracking
```typescript
const [previousTotalPrice, setPreviousTotalPrice] = useState<number>(totalPrice);
```

#### b) Effect để clear voucher khi cart thay đổi
```typescript
useEffect(() => {
  // Chỉ clear khi totalPrice thực sự thay đổi và có voucher được chọn
  if (totalPrice !== previousTotalPrice && formData.selectedVouchers.length > 0) {
    // Clear tất cả voucher đã chọn khi cart thay đổi
    setFormData(prev => ({ ...prev, selectedVouchers: [] }));

    // Hiển thị thông báo cho user
    toast.info(
      "Voucher đã được bỏ chọn do thay đổi giỏ hàng. Vui lòng chọn lại voucher phù hợp.",
      {
        position: "top-center",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      }
    );

    setPreviousTotalPrice(totalPrice);
  }
}, [totalPrice, previousTotalPrice, formData.selectedVouchers]);
```

### 2. Cart Change Detection

#### a) Thêm callback handler
```typescript
const handleCartChange = useCallback(() => {
  // Effect sẽ tự động chạy khi totalPrice thay đổi
  // Không cần logic phức tạp, chỉ để trigger re-render
}, []);
```

#### b) Pass callback xuống OrderSummary
```typescript
<OrderSummary
  // ... other props
  onCartChange={handleCartChange}
/>
```

### 3. OrderSummary Component Updates

**File: `full/frontend/src/modules/checkout/components/OrderSummary.tsx`**

#### a) Thêm callback prop
```typescript
interface OrderSummaryProps extends OrderSummaryType {
  // ... existing props
  onCartChange?: () => void; // Callback for cart changes
}
```

#### b) Gọi callback khi có cart changes
```typescript
const incrementQuantity = (productId: string, currentQuantity: number) => {
  updateQuantity(productId, currentQuantity + 1);
  onCartChange?.(); // Notify parent
};

const decrementQuantity = (productId: string, currentQuantity: number) => {
  if (currentQuantity > 1) {
    updateQuantity(productId, currentQuantity - 1);
    onCartChange?.(); // Notify parent
  }
};

const handleRemoveItem = (productId: string) => {
  removeItem(productId);
  onCartChange?.(); // Notify parent
};
```

## Flow Logic

### 1. User Actions
- User click tăng/giảm quantity
- User xóa sản phẩm khỏi cart

### 2. System Response
1. **OrderSummary**: Gọi cart action (updateQuantity/removeItem)
2. **CartContext**: Cập nhật cart state → totalPrice thay đổi
3. **OrderSummary**: Gọi `onCartChange()` callback
4. **CheckoutPage**: useEffect detect totalPrice change
5. **Auto-clear**: Clear tất cả voucher đã chọn + hiển thị thông báo
6. **User action**: User chọn lại voucher phù hợp (validation sẽ diễn ra tại VoucherSelect)

### 3. Clear Logic
```typescript
// Điều kiện clear voucher
totalPrice !== previousTotalPrice && selectedVouchers.length > 0

// Action
- Clear tất cả selectedVouchers = []
- Show toast notification: "Voucher đã được bỏ chọn do thay đổi giỏ hàng"
- Update previousTotalPrice
```

## User Experience

### 1. Thông báo rõ ràng
```typescript
toast.info(
  "Voucher đã được bỏ chọn do thay đổi giỏ hàng. Vui lòng chọn lại voucher phù hợp."
);
```

### 2. Simple handling
- Clear tất cả voucher khi cart thay đổi
- Không cần logic validation phức tạp
- User tự chọn lại voucher phù hợp

### 3. Real-time clearing
- Clear ngay khi totalPrice thay đổi
- Validation diễn ra khi user chọn voucher (tại VoucherSelect component)

## Benefits

### 1. Data Integrity
- Tránh confusion khi cart thay đổi mà voucher vẫn được giữ lại
- Đảm bảo user có cơ hội chọn lại voucher phù hợp

### 2. User Experience
- Thông báo rõ ràng khi voucher bị clear
- Logic đơn giản, dễ hiểu cho user

### 3. Business Logic
- Tránh áp dụng voucher với cart đã thay đổi
- Validation diễn ra tại thời điểm chọn voucher (không phải khi cart thay đổi)

## Files Modified

### Frontend
- `full/frontend/src/pages/CheckoutPage.tsx`
- `full/frontend/src/modules/checkout/components/OrderSummary.tsx`

### Key Changes
1. **CheckoutPage**: Thêm tracking totalPrice changes và voucher validation
2. **OrderSummary**: Thêm callback notification cho cart changes
3. **User Notification**: Toast messages cho voucher removal

## Testing Scenarios

### Test Case 1: Voucher becomes invalid
1. Chọn voucher có min_purchase_amount = 500k
2. Cart total = 600k → Voucher valid
3. Giảm quantity → Cart total = 400k
4. **Expected**: Voucher tự động bị loại bỏ + thông báo

### Test Case 2: Multiple vouchers
1. Chọn 2 voucher: A (min 300k), B (min 500k)  
2. Cart total = 600k → Cả 2 voucher valid
3. Giảm quantity → Cart total = 400k
4. **Expected**: Voucher B bị loại bỏ, voucher A được giữ lại

### Test Case 3: Voucher remains valid
1. Chọn voucher có min_purchase_amount = 300k
2. Cart total = 500k → Voucher valid
3. Giảm quantity → Cart total = 400k (vẫn > 300k)
4. **Expected**: Voucher được giữ lại, không có thông báo
