# Fix: Auto Clear Voucher on Cart Changes

## Mô tả yêu cầu
<PERSON> khách hàng thay đổi số lượng sản phẩm trong checkout page (làm thay đổi tổng tiền), hệ thống sẽ tự động kiểm tra và loại bỏ voucher đã chọn nếu không còn đáp ứng điều kiện áp dụng (min_purchase_amount).

## Vấn đề cần giải quyết
- User thay đổi quantity → totalPrice thay đổi
- Voucher đã chọn có thể không còn hợp lệ (totalPrice < min_purchase_amount)
- Cần tự động clear voucher để tránh sai sót trong logic tính toán

## Giải pháp thực hiện

### 1. Tracking Total Price Changes

**File: `full/frontend/src/pages/CheckoutPage.tsx`**

#### a) Thêm state tracking
```typescript
const [previousTotalPrice, setPreviousTotalPrice] = useState<number>(totalPrice);
```

#### b) Effect để detect thay đổi totalPrice
```typescript
useEffect(() => {
  // Chỉ check khi totalPrice thực sự thay đổi và có voucher được chọn
  if (totalPrice !== previousTotalPrice && formData.selectedVouchers.length > 0) {
    const invalidVouchers: Voucher[] = [];
    
    // Kiểm tra từng voucher với totalPrice mới
    formData.selectedVouchers.forEach(voucher => {
      if (voucher.min_purchase_amount > totalPrice) {
        invalidVouchers.push(voucher);
      }
    });

    // Nếu có voucher không hợp lệ, loại bỏ và thông báo
    if (invalidVouchers.length > 0) {
      const remainingVouchers = formData.selectedVouchers.filter(
        voucher => !invalidVouchers.some(invalid => invalid.id === voucher.id)
      );
      
      setFormData(prev => ({ ...prev, selectedVouchers: remainingVouchers }));
      
      // Hiển thị thông báo
      invalidVouchers.forEach(voucher => {
        toast.warning(
          `Voucher ${voucher.code} đã bị loại bỏ vì đơn hàng không đạt giá trị tối thiểu ${formatCurrency(voucher.min_purchase_amount)}`
        );
      });
    }
    
    setPreviousTotalPrice(totalPrice);
  }
}, [totalPrice, previousTotalPrice, formData.selectedVouchers]);
```

### 2. Cart Change Detection

#### a) Thêm callback handler
```typescript
const handleCartChange = useCallback(() => {
  // Effect sẽ tự động chạy khi totalPrice thay đổi
}, []);
```

#### b) Pass callback xuống OrderSummary
```typescript
<OrderSummary
  // ... other props
  onCartChange={handleCartChange}
/>
```

### 3. OrderSummary Component Updates

**File: `full/frontend/src/modules/checkout/components/OrderSummary.tsx`**

#### a) Thêm callback prop
```typescript
interface OrderSummaryProps extends OrderSummaryType {
  // ... existing props
  onCartChange?: () => void; // Callback for cart changes
}
```

#### b) Gọi callback khi có cart changes
```typescript
const incrementQuantity = (productId: string, currentQuantity: number) => {
  updateQuantity(productId, currentQuantity + 1);
  onCartChange?.(); // Notify parent
};

const decrementQuantity = (productId: string, currentQuantity: number) => {
  if (currentQuantity > 1) {
    updateQuantity(productId, currentQuantity - 1);
    onCartChange?.(); // Notify parent
  }
};

const handleRemoveItem = (productId: string) => {
  removeItem(productId);
  onCartChange?.(); // Notify parent
};
```

## Flow Logic

### 1. User Actions
- User click tăng/giảm quantity
- User xóa sản phẩm khỏi cart

### 2. System Response
1. **OrderSummary**: Gọi cart action (updateQuantity/removeItem)
2. **CartContext**: Cập nhật cart state → totalPrice thay đổi
3. **OrderSummary**: Gọi `onCartChange()` callback
4. **CheckoutPage**: useEffect detect totalPrice change
5. **Validation**: Kiểm tra voucher validity với totalPrice mới
6. **Auto-clear**: Loại bỏ voucher không hợp lệ + hiển thị thông báo

### 3. Validation Logic
```typescript
// Kiểm tra điều kiện voucher
voucher.min_purchase_amount > totalPrice → Invalid voucher

// Action
- Remove từ selectedVouchers
- Show toast notification
- Update previousTotalPrice
```

## User Experience

### 1. Thông báo rõ ràng
```typescript
toast.warning(
  `Voucher ${voucher.code} đã bị loại bỏ vì đơn hàng không đạt giá trị tối thiểu ${formatCurrency(voucher.min_purchase_amount)}`
);
```

### 2. Automatic handling
- Không cần user action
- Tự động loại bỏ voucher không hợp lệ
- Giữ lại voucher còn hợp lệ

### 3. Real-time validation
- Kiểm tra ngay khi totalPrice thay đổi
- Không chờ đến khi submit form

## Benefits

### 1. Data Integrity
- Đảm bảo voucher luôn hợp lệ với cart hiện tại
- Tránh lỗi khi submit order

### 2. User Experience
- Thông báo rõ ràng khi voucher bị loại bỏ
- Tự động xử lý, không cần user can thiệp

### 3. Business Logic
- Đảm bảo điều kiện áp dụng voucher được tuân thủ
- Tránh áp dụng voucher sai điều kiện

## Files Modified

### Frontend
- `full/frontend/src/pages/CheckoutPage.tsx`
- `full/frontend/src/modules/checkout/components/OrderSummary.tsx`

### Key Changes
1. **CheckoutPage**: Thêm tracking totalPrice changes và voucher validation
2. **OrderSummary**: Thêm callback notification cho cart changes
3. **User Notification**: Toast messages cho voucher removal

## Testing Scenarios

### Test Case 1: Voucher becomes invalid
1. Chọn voucher có min_purchase_amount = 500k
2. Cart total = 600k → Voucher valid
3. Giảm quantity → Cart total = 400k
4. **Expected**: Voucher tự động bị loại bỏ + thông báo

### Test Case 2: Multiple vouchers
1. Chọn 2 voucher: A (min 300k), B (min 500k)  
2. Cart total = 600k → Cả 2 voucher valid
3. Giảm quantity → Cart total = 400k
4. **Expected**: Voucher B bị loại bỏ, voucher A được giữ lại

### Test Case 3: Voucher remains valid
1. Chọn voucher có min_purchase_amount = 300k
2. Cart total = 500k → Voucher valid
3. Giảm quantity → Cart total = 400k (vẫn > 300k)
4. **Expected**: Voucher được giữ lại, không có thông báo
