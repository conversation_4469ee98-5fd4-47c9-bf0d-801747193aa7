import { useState, useEffect } from 'react';

// Enhanced breakpoints for better mobile/tablet targeting
const breakpoints = {
  xs: 375,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

// Device-specific breakpoints
const deviceBreakpoints = {
  mobile: 768,    // < 768px
  tablet: 1024,   // 768px - 1024px
  desktop: 1024,  // >= 1024px
};

export type Breakpoint = keyof typeof breakpoints;
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

export function useBreakpoint(breakpoint: Breakpoint): boolean {
  const [isAboveBreakpoint, setIsAboveBreakpoint] = useState(
    typeof window !== 'undefined' ? window.innerWidth >= breakpoints[breakpoint] : false
  );

  useEffect(() => {
    const checkSize = () => {
      setIsAboveBreakpoint(window.innerWidth >= breakpoints[breakpoint]);
    };

    window.addEventListener('resize', checkSize);
    checkSize(); // Check on mount

    return () => window.removeEventListener('resize', checkSize);
  }, [breakpoint]);

  return isAboveBreakpoint;
}

export function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = useState(
    typeof window !== 'undefined' ? window.innerWidth < deviceBreakpoints.mobile : false
  );

  useEffect(() => {
    const checkSize = () => {
      setIsMobile(window.innerWidth < deviceBreakpoints.mobile);
    };

    window.addEventListener('resize', checkSize);
    checkSize(); // Check on mount

    return () => window.removeEventListener('resize', checkSize);
  }, []);

  return isMobile;
}

export function useIsTablet(): boolean {
  const [isTablet, setIsTablet] = useState(
    typeof window !== 'undefined'
      ? window.innerWidth >= deviceBreakpoints.mobile && window.innerWidth < deviceBreakpoints.desktop
      : false
  );

  useEffect(() => {
    const checkSize = () => {
      const width = window.innerWidth;
      setIsTablet(width >= deviceBreakpoints.mobile && width < deviceBreakpoints.desktop);
    };

    window.addEventListener('resize', checkSize);
    checkSize(); // Check on mount

    return () => window.removeEventListener('resize', checkSize);
  }, []);

  return isTablet;
}

export function useDeviceType(): DeviceType {
  const [deviceType, setDeviceType] = useState<DeviceType>(() => {
    if (typeof window === 'undefined') return 'desktop';
    const width = window.innerWidth;
    if (width < deviceBreakpoints.mobile) return 'mobile';
    if (width < deviceBreakpoints.desktop) return 'tablet';
    return 'desktop';
  });

  useEffect(() => {
    const checkSize = () => {
      const width = window.innerWidth;
      if (width < deviceBreakpoints.mobile) {
        setDeviceType('mobile');
      } else if (width < deviceBreakpoints.desktop) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    window.addEventListener('resize', checkSize);
    checkSize(); // Check on mount

    return () => window.removeEventListener('resize', checkSize);
  }, []);

  return deviceType;
}

// Touch device detection
export function useIsTouchDevice(): boolean {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    const checkTouch = () => {
      setIsTouchDevice('ontouchstart' in window || navigator.maxTouchPoints > 0);
    };

    checkTouch();
  }, []);

  return isTouchDevice;
}

// Responsive utilities
export const responsiveUtils = {
  // Touch-friendly minimum sizes
  touchTarget: {
    minHeight: '44px',
    minWidth: '44px',
  },

  // Responsive spacing
  spacing: {
    mobile: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
    },
    tablet: {
      xs: '0.5rem',
      sm: '0.75rem',
      md: '1.25rem',
      lg: '2rem',
      xl: '2.5rem',
    },
    desktop: {
      xs: '0.5rem',
      sm: '1rem',
      md: '1.5rem',
      lg: '2rem',
      xl: '3rem',
    },
  },

  // Responsive font sizes
  fontSize: {
    mobile: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
    },
    tablet: {
      xs: '0.875rem',
      sm: '1rem',
      base: '1.125rem',
      lg: '1.25rem',
      xl: '1.5rem',
      '2xl': '1.875rem',
    },
    desktop: {
      xs: '0.875rem',
      sm: '1rem',
      base: '1.125rem',
      lg: '1.25rem',
      xl: '1.5rem',
      '2xl': '2rem',
    },
  },
};
