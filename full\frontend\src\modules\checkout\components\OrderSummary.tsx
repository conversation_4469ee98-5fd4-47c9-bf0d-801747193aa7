import React from "react";
import { Clock, Plus, Minus, Trash2 } from "lucide-react";
import { ProductData } from "@/lib/api";
import { useCart } from "@/context/CartContext";
import { OrderSummary as OrderSummaryType } from "../types";
import { Voucher } from "@/lib/api/voucher/voucher";
import { BASE_SHIPPING_FEE } from "../utils";

const formatPrice = (price: number): string => {
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
    maximumFractionDigits: 0,
  }).format(price);
};

interface OrderSummaryProps extends OrderSummaryType {
  isSubmitting: boolean;
  isFormValid: boolean;
  formId?: string;
  isMobile?: boolean;
  selectedVouchers: Voucher[];
  selectedDistrict?: string; // Keep for potential future use, but fee calculation is now external
  shippingFee: number | null; // Correct prop name
  shippingError: string | null; // Correct prop name
  onCartChange?: () => void; // Callback for cart changes
}

export function OrderSummaryComponent({
  items,
  totalPrice,
  isSubmitting,
  isFormValid,
  formId = "checkoutForm",
  isMobile = false,
  selectedVouchers,
  selectedDistrict, // Keep prop, but calculation is external
  shippingFee, // Use correct prop name in destructuring
  shippingError, // Use correct prop name in destructuring
  onCartChange, // Callback for cart changes
}: OrderSummaryProps) {
  // const baseShippingFee = 30000; // Base shipping fee for voucher calculations - Keep for freeship voucher logic?
  const { updateQuantity, removeItem } = useCart();

  const incrementQuantity = (productId: string, currentQuantity: number) => {
    updateQuantity(productId, currentQuantity + 1);
    // Notify parent component about cart change
    onCartChange?.();
  };

  const decrementQuantity = (productId: string, currentQuantity: number) => {
    if (currentQuantity > 1) {
      updateQuantity(productId, currentQuantity - 1);
      // Notify parent component about cart change
      onCartChange?.();
    }
  };

  const handleRemoveItem = (productId: string) => {
    removeItem(productId);
    // Notify parent component about cart change
    onCartChange?.();
  };

  // Use the externally calculated shipping fee
  const displayShippingFee = shippingFee ?? 0; // Use correct prop name, default to 0 if null for calculation
  const subtotal = totalPrice + displayShippingFee;

  // Calculate voucher discount - Keep freeship logic based on BASE_SHIPPING_FEE for now
  // TODO: Revisit if freeship should cover the *dynamic* fee instead
  const totalVoucherDiscount = selectedVouchers.reduce((total, voucher) => {
    // Check if it's a freeship voucher (value 0, not percentage)
    if (Number(voucher.value).toFixed(2) === "0.00" && !voucher.is_percentage) {
      // Apply the *actual calculated* shipping fee as the discount amount for freeship
      // If the fee hasn't been calculated yet (shippingFee is null), default discount to 0 for now.
      const freeshipDiscount = shippingFee ?? 0;
      return total + freeshipDiscount;
    }
    // Apply percentage or fixed amount discount
    const discountValue = voucher.is_percentage
      ? (voucher.value / 100) * totalPrice
      : Number(voucher.value);
    return total + discountValue;
  }, 0);

  // Calculate final total, ensuring it doesn't go below zero
  const finalTotal = Math.max(0, subtotal - totalVoucherDiscount);

  const buttonClasses = `w-full py-4 rounded-lg text-lg font-semibold ${
    isFormValid
      ? "primary-gradient text-white cursor-pointer hover:opacity-90 transition-opacity"
      : "bg-gray-300 text-gray-500 cursor-not-allowed"
  }`;

  const submitButton = (
    <button
      type="submit"
      form={formId}
      disabled={isSubmitting || !isFormValid}
      className={buttonClasses}
    >
      {isSubmitting ? "Đang xử lý..." : "ĐẶT HÀNG"}
    </button>
  );

  return (
    <div
      className={`bg-white p-6 rounded-lg shadow-sm border border-gray-200 ${
        isMobile ? "mt-6" : ""
      } w-full max-w-none`}
    >
      <h3 className="text-lg font-semibold text-gray-800 mb-4">
        Thông tin đơn hàng
      </h3>

      {/* Cart Items */}
      <div className="divide-y mb-4">
        {items.map((item) => (
          <div key={item.id} className="py-4 flex gap-4 items-center">
            <button
              type="button"
              onClick={() => handleRemoveItem(item.id)}
              className="text-red-500 hover:text-red-700 flex-shrink-0"
              title="Xóa sản phẩm"
            >
              <Trash2 className="w-5 h-5" />
            </button>

            <div className="relative w-20 h-20 border rounded overflow-hidden flex-shrink-0">
              <img
                src={item.thumbnail}
                alt={item.title}
                className="w-full h-full object-cover"
              />
              <span className="absolute top-0 right-0 bg-gray-500 text-white text-xs px-1 rounded-bl">
                {item.quantity}
              </span>
            </div>

            <div className="flex-1">
              <h3 className="text-base text-gray-800 font-medium line-clamp-2">
                {item.title}
              </h3>
              <div className="text-sm text-gray-600 mt-1">
                {formatPrice(item.discountedPrice || item.price)} x {item.quantity}
              </div>
            </div>

            <div className="flex flex-col items-end">
              <div className="aurora-text font-medium text-lg mb-2">
                {formatPrice((item.discountedPrice || item.price) * item.quantity)}
              </div>
              <div className="flex items-center border rounded-md bg-white text-sm">
                <button
                  type="button"
                  onClick={() => decrementQuantity(item.id, item.quantity)}
                  className="p-1 hover:bg-gray-100"
                >
                  <Minus className="w-3.5 h-3.5" />
                </button>
                <span className="px-2 py-0.5">{item.quantity}</span>
                <button
                  type="button"
                  onClick={() => incrementQuantity(item.id, item.quantity)}
                  className="p-1 hover:bg-gray-100"
                >
                  <Plus className="w-3.5 h-3.5" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Price Summary */}
      <div className="border-t border-gray-200 pt-5 space-y-4">
        <div className="flex justify-between text-gray-600 text-base">
          <span>Tạm tính</span>
          <span className="font-medium">{formatPrice(totalPrice)}</span>
        </div>

        <div className="flex justify-between text-gray-600 text-base">
          <span>Phí vận chuyển</span>
          {shippingError ? ( // Use correct prop name
            <span className="text-red-500">{shippingError}</span>
          ) : shippingFee === null ? ( // Use correct prop name
            <span className="text-gray-500">Nhập địa chỉ để tính phí</span>
          ) : shippingFee === 0 ? ( // Check if fee is exactly 0
            <span className="text-green-600 font-medium">Miễn phí</span> // Display "Miễn phí"
          ) : (
            // Otherwise, display the formatted fee
            <span className="font-medium">{formatPrice(shippingFee)}</span>
          )}
        </div>

        {selectedVouchers.map((voucher) => (
          <div
            key={voucher.id}
            className="flex justify-between text-gray-600 text-base"
          >
            <span className="flex items-center">Giảm giá ({voucher.code})</span>
            <span className="text-green-600 font-medium">
              {Number(voucher.value).toFixed(2) === "0.00" &&
              !voucher.is_percentage
                ? `-${formatPrice(shippingFee ?? 0)}` // Show the actual calculated fee (or 0 if null) as discount
                : voucher.is_percentage
                ? `-${formatPrice((voucher.value / 100) * totalPrice)}`
                : `-${formatPrice(voucher.value)}`}
            </span>
          </div>
        ))}

        <div className="flex justify-between font-semibold text-xl pt-4 border-t border-gray-200">
          <span>Tổng cộng</span>
          <span className="text-primary">{formatPrice(finalTotal)}</span>
        </div>
      </div>

      {/* Estimated Delivery */}
      <div className="mt-5 bg-gray-50 p-4 rounded-md flex items-center gap-3">
        <Clock size={20} className="text-gray-500" />
        <span className="text-base text-gray-600">
          Đơn hàng sẽ được giao sớm cho bạn
        </span>
      </div>

      {/* Submit Button */}
      {isMobile ? (
        <div className="mt-6 lg:hidden">{submitButton}</div>
      ) : (
        <div className="hidden lg:block mt-6">{submitButton}</div>
      )}
    </div>
  );
}
