from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db import transaction
from django.db.models import F
from store.models import Promotion, Order, OrderPromotion
from store.serializers.order_serializers import OrderPromotionSerializer
from store.serializers.cart_serializers import PromotionSerializer
from store.permissions import IsAdminOrReadOnly

class PromotionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing promotions.
    Provides CRUD operations for admins and read-only access for authenticated users.
    """
    queryset = Promotion.objects.all()
    serializer_class = PromotionSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminOrReadOnly]

    def get_queryset(self):
        """
        Override to filter valid promotions for non-admin users.
        Admins can see all promotions, regular users only see valid ones.
        """
        queryset = super().get_queryset()

        # If user is admin, return all promotions
        if self.request.user.is_authenticated and self.request.user.is_staff:
            return queryset

        # For regular users, filter only valid promotions
        now = timezone.now()
        return queryset.filter(
            is_active=True,
            valid_from__lte=now,
            valid_until__gte=now,
            usage_count__lt=F('usage_limit')
        )

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def public(self, request):
        """
        Public endpoint to get valid promotions only.
        Accessible without authentication.
        """
        now = timezone.now()
        queryset = self.queryset.filter(
            is_active=True,
            valid_from__lte=now,
            valid_until__gte=now,
            usage_count__lt=F('usage_limit')
        )
        serializer = self.get_serializer(queryset, many=True)
        return Response({'results': serializer.data})

    @action(detail=True, methods=['post'])
    def validate(self, request, pk=None):
        """
        Validate a promotion for use.
        Checks:
        - Promotion exists and is active
        - Within validity period
        - Usage limit not exceeded
        - Minimum purchase amount met
        """
        promotion = self.get_object()
        amount = request.data.get('amount', 0)

        if not promotion.is_valid:
            return Response(
                {"detail": "Promotion is not valid"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if amount < promotion.min_purchase_amount:
            return Response(
                {
                    "detail": f"Total amount must be at least {promotion.min_purchase_amount}đ"
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        discount = promotion.calculate_discount(amount)
        return Response({
            "valid": True,
            "discount_amount": discount
        })

    @action(detail=True, methods=['post'])
    def apply_to_order(self, request, pk=None):
        """Apply a promotion to an order"""
        promotion = self.get_object()
        order_id = request.data.get('order_id')

        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            return Response(
                {"detail": "Order not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if promotion is already applied to this order
        if OrderPromotion.objects.filter(order=order, promotion=promotion).exists():
            return Response(
                {"detail": "Promotion already applied to this order"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate promotion
        if not promotion.is_valid:
            return Response(
                {"detail": "Promotion is not valid"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if order.total_price < promotion.min_purchase_amount:
            return Response(
                {
                    "detail": f"Order total must be at least {promotion.min_purchase_amount}đ"
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Calculate discount
                discount = promotion.calculate_discount(order.total_price)

                # Create OrderPromotion
                order_promotion = OrderPromotion.objects.create(
                    order=order,
                    promotion=promotion,
                    discount_amount=discount
                )

                # Update promotion usage count
                promotion.usage_count += 1
                promotion.save()

            serializer = OrderPromotionSerializer(order_promotion)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {"detail": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def remove_from_order(self, request, pk=None):
        """Remove a promotion from an order"""
        promotion = self.get_object()
        order_id = request.data.get('order_id')

        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            return Response(
                {"detail": "Order not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            order_promotion = OrderPromotion.objects.get(
                order=order,
                promotion=promotion
            )
        except OrderPromotion.DoesNotExist:
            return Response(
                {"detail": "Promotion not applied to this order"},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            with transaction.atomic():
                # Delete OrderPromotion
                order_promotion.delete()

                # Update promotion usage count
                promotion.usage_count = max(0, promotion.usage_count - 1)
                promotion.save()

            return Response(status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            return Response(
                {"detail": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
