# Fix: Promotion Validity Filtering

## <PERSON><PERSON> tả yêu cầu
<PERSON><PERSON> lý các promotion không còn valid (expired/full) để không render voucher đó ở frontend. Backend sẽ kiểm tra validity và chỉ trả về các promotion valid.

## Các thay đổi đã thực hiện

### 1. Backend Changes

#### a) Cập nhật PromotionViewSet (`full/backend/store/views/promotion_views.py`)

**Thêm filtering logic:**
- Override `get_queryset()` để filter promotion valid cho user thường
- Admin vẫn có thể xem tất cả promotion
- Cập nhật `public()` endpoint để chỉ trả về promotion valid

**Điều kiện valid:**
- `is_active = True`
- `valid_from <= now <= valid_until` (không expired)
- `usage_count < usage_limit` (không full)

```python
def get_queryset(self):
    queryset = super().get_queryset()
    
    # Admin xem tất cả
    if self.request.user.is_authenticated and self.request.user.is_staff:
        return queryset
    
    # User thường chỉ xem promotion valid
    now = timezone.now()
    return queryset.filter(
        is_active=True,
        valid_from__lte=now,
        valid_until__gte=now,
        usage_count__lt=F('usage_limit')
    )
```

#### b) Import thêm F object
```python
from django.db.models import F
```

### 2. Frontend Changes

#### a) Cập nhật Voucher interface (`full/frontend/src/lib/api/voucher/voucher.ts`)

**Thêm fields validation:**
```typescript
export interface Voucher {
  // ... existing fields
  usage_limit: number;
  usage_count: number;
  valid_from: string;
  valid_until: string;
}
```

#### b) Đơn giản hóa API call
- Loại bỏ filtering ở frontend vì backend đã xử lý
- Chỉ giữ lại logic add default image

```typescript
// Backend trả về voucher valid, chỉ cần add default image
const vouchers = response.data.results.map((voucher: Voucher) => ({
  ...voucher,
  image_url: voucher.image_url || DEFAULT_VOUCHER_IMAGE
}));
```

#### c) Cập nhật VoucherSelect component
- Loại bỏ filtering redundant ở frontend
- Backend đã đảm bảo chỉ trả về voucher valid

## Kết quả

### API Endpoints
1. **GET /api/promotions** (authenticated)
   - Admin: Trả về tất cả promotion
   - User: Chỉ trả về promotion valid

2. **GET /api/promotions/public** (public)
   - Chỉ trả về promotion valid

### Validation Logic
Backend tự động filter promotion dựa trên:
- ✅ `is_active = True`
- ✅ Chưa expired (`valid_from <= now <= valid_until`)
- ✅ Chưa full (`usage_count < usage_limit`)

### Frontend
- Nhận danh sách voucher đã được filter
- Không cần validation logic phức tạp
- UI chỉ hiển thị voucher có thể sử dụng

## Testing

### Test Cases
1. **Promotion expired**: Không xuất hiện trong danh sách
2. **Promotion full (usage_count >= usage_limit)**: Không xuất hiện
3. **Promotion inactive**: Không xuất hiện
4. **Promotion valid**: Xuất hiện bình thường
5. **Admin user**: Xem được tất cả promotion
6. **Regular user**: Chỉ xem promotion valid

### Cách test
```bash
# Test API endpoint
curl -X GET "http://localhost:8000/api/promotions/public"

# Kiểm tra response chỉ chứa promotion valid
# Không có promotion expired hoặc full
```

## Lợi ích

1. **Performance**: Giảm data transfer, frontend nhận ít data hơn
2. **Security**: Backend control validation logic
3. **Maintainability**: Logic tập trung ở backend
4. **User Experience**: User chỉ thấy voucher có thể dùng
5. **Consistency**: Đảm bảo data consistency across platform

## Files Modified

### Backend
- `full/backend/store/views/promotion_views.py`
- `full/backend/store/serializers/cart_serializers.py`

### Frontend  
- `full/frontend/src/lib/api/voucher/voucher.ts`
- `full/frontend/src/modules/checkout/components/VoucherSelect.tsx`
