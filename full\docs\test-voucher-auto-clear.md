# Test Cases: Voucher Auto-Clear on Cart Changes

## Test Setup

### Prerequisites
1. <PERSON><PERSON> ít nhất 2 voucher trong hệ thống:
   - Voucher A: min_purchase_amount = 300,000 VND
   - Voucher B: min_purchase_amount = 500,000 VND
2. <PERSON><PERSON> sản phẩm trong cart với tổng giá trị > 500,000 VND

## Test Case 1: Voucher Clear on Quantity Change

### Steps:
1. Vào checkout page
2. Chọn bất kỳ voucher nào (ví dụ: Voucher B)
3. Verify voucher được apply thành công
4. Thay đổi quantity sản phẩm (tăng hoặc giảm)
5. Observe behavior

### Expected Results:
- ✅ Voucher được clear khỏi selectedVouchers (bất kể có hợp lệ hay không)
- ✅ Toast notification hiển thị: "Voucher đã được bỏ chọn do thay đổi giỏ hàng. <PERSON>ui lòng chọn lại voucher phù hợp."
- ✅ Order summary cập nhật không còn hiển thị voucher discount
- ✅ VoucherSelect component reset về trạng thái chưa chọn voucher

## Test Case 2: Remove Item from Cart

### Steps:
1. Vào checkout page với multiple items
2. Chọn bất kỳ voucher nào
3. Verify voucher được apply thành công
4. Xóa 1 item khỏi cart
5. Observe behavior

### Expected Results:
- ✅ Voucher được clear khỏi selectedVouchers
- ✅ Toast notification hiển thị
- ✅ Order summary cập nhật với cart mới (không có voucher)
- ✅ User có thể chọn lại voucher phù hợp với cart mới

## Test Case 3: Increase Quantity

### Steps:
1. Vào checkout page với cart total = 300k
2. Chọn Voucher A (min 300k)
3. Tăng quantity để cart total = 500k
4. Observe behavior

### Expected Results:
- ✅ Voucher A được clear (mặc dù vẫn hợp lệ với cart mới)
- ✅ Toast notification hiển thị
- ✅ User có thể chọn lại voucher (có thể chọn Voucher B với cart total mới)

## Test Case 4: No Voucher Selected

### Steps:
1. Vào checkout page
2. Không chọn voucher nào
3. Thay đổi quantity sản phẩm
4. Observe behavior

### Expected Results:
- ✅ Không có thay đổi gì (không có voucher để clear)
- ✅ Không có toast notification
- ✅ Cart cập nhật bình thường

## Test Case 5: Multiple Rapid Changes

### Steps:
1. Vào checkout page
2. Chọn voucher
3. Thay đổi quantity nhiều lần liên tiếp (rapid clicks)
4. Observe behavior

### Expected Results:
- ✅ Voucher clear sau lần thay đổi đầu tiên
- ✅ Chỉ 1 toast notification (không spam notifications)
- ✅ Các lần thay đổi tiếp theo không trigger thêm notifications

## Test Case 6: Freeship Voucher

### Steps:
1. Vào checkout page với cart total = 700k
2. Chọn freeship voucher
3. Thay đổi quantity
4. Observe behavior

### Expected Results:
- ✅ Freeship voucher được clear
- ✅ Shipping fee hiển thị lại bình thường
- ✅ Toast notification hiển thị

## Manual Testing Checklist

### UI/UX Verification:
- [ ] Toast notifications hiển thị đúng vị trí (top-center)
- [ ] Toast auto-close sau 5 seconds
- [ ] Voucher selection UI reset về trạng thái ban đầu
- [ ] Order summary cập nhật real-time
- [ ] Không có flickering hoặc UI glitches

### Performance Verification:
- [ ] useEffect không trigger vô hạn lần
- [ ] Không có memory leaks
- [ ] Response time < 100ms cho validation

### Edge Cases:
- [ ] Cart total = exactly min_purchase_amount (should keep voucher)
- [ ] Multiple rapid quantity changes
- [ ] Network interruption during validation
- [ ] Browser refresh after voucher selection

## Automated Testing (Future)

### Unit Tests:
```typescript
describe('Voucher Auto-Clear Logic', () => {
  test('should remove voucher when cart total below minimum', () => {
    // Test logic
  });
  
  test('should keep voucher when cart total meets minimum', () => {
    // Test logic
  });
  
  test('should handle multiple vouchers correctly', () => {
    // Test logic
  });
});
```

### Integration Tests:
- Test với real API calls
- Test với different voucher types
- Test với concurrent user actions

## Bug Report Template

### If issues found:
```
**Bug**: Voucher auto-clear not working

**Steps to reproduce**:
1. [Detailed steps]

**Expected**: [Expected behavior]
**Actual**: [Actual behavior]

**Environment**:
- Browser: [Chrome/Firefox/Safari]
- Cart total: [Amount]
- Voucher details: [Code, min_purchase_amount]

**Console errors**: [Any JavaScript errors]
```

## Success Criteria

### All test cases pass with:
- ✅ Correct voucher removal logic
- ✅ Appropriate user notifications  
- ✅ Accurate price calculations
- ✅ Smooth user experience
- ✅ No console errors
- ✅ Consistent behavior across browsers
