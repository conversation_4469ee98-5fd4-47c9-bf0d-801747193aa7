# Test Cases: Voucher Auto-Clear on Cart Changes

## Test Setup

### Prerequisites
1. <PERSON><PERSON> ít nhất 2 voucher trong hệ thống:
   - Voucher A: min_purchase_amount = 300,000 VND
   - Voucher B: min_purchase_amount = 500,000 VND
2. <PERSON><PERSON> sản phẩm trong cart với tổng giá trị > 500,000 VND

## Test Case 1: Single Voucher Becomes Invalid

### Steps:
1. Vào checkout page
2. Chọn Voucher B (min 500k)
3. Verify voucher được apply thành công
4. Giảm quantity sản phẩm để cart total < 500k (ví dụ: 400k)
5. Observe behavior

### Expected Results:
- ✅ Voucher B tự động bị loại bỏ khỏi selectedVouchers
- ✅ Toast notification hiển thị: "Voucher [CODE] đã bị loại bỏ vì đơn hàng không đạt giá trị tối thiểu 500,000₫"
- ✅ Order summary cập nhật không còn hiển thị voucher discount
- ✅ Total price được tính lại chính xác

## Test Case 2: Multiple Vouchers - Partial Removal

### Steps:
1. Vào checkout page với cart total = 600k
2. Chọn Voucher A (min 300k) 
3. Chọn Voucher B (min 500k) - replace voucher A
4. Giảm quantity để cart total = 400k
5. Observe behavior

### Expected Results:
- ✅ Voucher B bị loại bỏ (400k < 500k)
- ✅ Toast notification cho Voucher B
- ✅ Voucher selection reset về empty state
- ✅ User có thể chọn lại Voucher A (vì 400k > 300k)

## Test Case 3: Voucher Remains Valid

### Steps:
1. Vào checkout page với cart total = 500k
2. Chọn Voucher A (min 300k)
3. Giảm quantity để cart total = 400k (vẫn > 300k)
4. Observe behavior

### Expected Results:
- ✅ Voucher A được giữ lại
- ✅ Không có toast notification
- ✅ Order summary vẫn hiển thị voucher discount
- ✅ Total price được tính với voucher discount

## Test Case 4: Remove Item Completely

### Steps:
1. Vào checkout page với 2 items, total = 600k
2. Chọn Voucher B (min 500k)
3. Xóa 1 item để total = 300k
4. Observe behavior

### Expected Results:
- ✅ Voucher B bị loại bỏ (300k < 500k)
- ✅ Toast notification hiển thị
- ✅ Order summary cập nhật

## Test Case 5: Increase Quantity (Edge Case)

### Steps:
1. Vào checkout page với cart total = 450k
2. Không chọn voucher nào (vì không đủ điều kiện cho Voucher B)
3. Tăng quantity để cart total = 550k
4. Chọn Voucher B (min 500k)
5. Giảm quantity về 450k
6. Observe behavior

### Expected Results:
- ✅ Voucher B bị loại bỏ khi giảm quantity
- ✅ Toast notification hiển thị

## Test Case 6: Freeship Voucher (Special Case)

### Steps:
1. Vào checkout page với cart total = 700k
2. Chọn freeship voucher (value = 0, min_purchase_amount = 600k)
3. Giảm quantity để cart total = 500k
4. Observe behavior

### Expected Results:
- ✅ Freeship voucher bị loại bỏ (500k < 600k)
- ✅ Shipping fee hiển thị lại bình thường
- ✅ Toast notification hiển thị

## Manual Testing Checklist

### UI/UX Verification:
- [ ] Toast notifications hiển thị đúng vị trí (top-center)
- [ ] Toast auto-close sau 5 seconds
- [ ] Voucher selection UI reset về trạng thái ban đầu
- [ ] Order summary cập nhật real-time
- [ ] Không có flickering hoặc UI glitches

### Performance Verification:
- [ ] useEffect không trigger vô hạn lần
- [ ] Không có memory leaks
- [ ] Response time < 100ms cho validation

### Edge Cases:
- [ ] Cart total = exactly min_purchase_amount (should keep voucher)
- [ ] Multiple rapid quantity changes
- [ ] Network interruption during validation
- [ ] Browser refresh after voucher selection

## Automated Testing (Future)

### Unit Tests:
```typescript
describe('Voucher Auto-Clear Logic', () => {
  test('should remove voucher when cart total below minimum', () => {
    // Test logic
  });
  
  test('should keep voucher when cart total meets minimum', () => {
    // Test logic
  });
  
  test('should handle multiple vouchers correctly', () => {
    // Test logic
  });
});
```

### Integration Tests:
- Test với real API calls
- Test với different voucher types
- Test với concurrent user actions

## Bug Report Template

### If issues found:
```
**Bug**: Voucher auto-clear not working

**Steps to reproduce**:
1. [Detailed steps]

**Expected**: [Expected behavior]
**Actual**: [Actual behavior]

**Environment**:
- Browser: [Chrome/Firefox/Safari]
- Cart total: [Amount]
- Voucher details: [Code, min_purchase_amount]

**Console errors**: [Any JavaScript errors]
```

## Success Criteria

### All test cases pass with:
- ✅ Correct voucher removal logic
- ✅ Appropriate user notifications  
- ✅ Accurate price calculations
- ✅ Smooth user experience
- ✅ No console errors
- ✅ Consistent behavior across browsers
