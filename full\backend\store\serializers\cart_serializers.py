"""
Cart-related serializers
"""
from rest_framework import serializers
from ..models import Cart, CartItem, CartPromotion, Promotion, ProductVariant

class CartItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True, allow_null=True)
    product_price = serializers.SerializerMethodField()
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = CartItem
        fields = ['id', 'product', 'product_name', 'variant', 'variant_name', 'product_price', 'quantity', 'total_price']
        read_only_fields = ['id']
    
    def get_product_price(self, obj):
        if obj.variant:
            return obj.variant.price
        return obj.product.price

class CartItemAddSerializer(serializers.ModelSerializer):
    variant = serializers.PrimaryKeyRelatedField(
        queryset=ProductVariant.objects.filter(is_active=True),
        required=False,
        allow_null=True
    )
    
    class Meta:
        model = CartItem
        fields = ['product', 'variant', 'quantity']
    
    def validate(self, attrs):
        product = attrs['product']
        variant = attrs.get('variant')
        quantity = attrs['quantity']
        
        # Validate that variant belongs to product
        if variant and variant.product != product:
            raise serializers.ValidationError({"variant": "This variant does not belong to the selected product."})
        
        # Check stock availability
        if variant:
            if quantity > variant.stock:
                raise serializers.ValidationError({"quantity": f"Not enough stock available. Only {variant.stock} units left."})
        else:
            if quantity > product.stock:
                raise serializers.ValidationError({"quantity": f"Not enough stock available. Only {product.stock} units left."})
        
        return attrs
    
    def create(self, validated_data):
        cart = self.context['cart']
        product = validated_data['product']
        variant = validated_data.get('variant')
        quantity = validated_data['quantity']
        
        # Check if the item already exists in the cart
        try:
            cart_item = CartItem.objects.get(cart=cart, product=product, variant=variant)
            cart_item.quantity += quantity
            cart_item.save()
        except CartItem.DoesNotExist:
            cart_item = CartItem.objects.create(cart=cart, **validated_data)
            
        return cart_item

class PromotionSerializer(serializers.ModelSerializer):
    """Serializer for Promotion model"""
    value_display = serializers.SerializerMethodField()
    image_url = serializers.SerializerMethodField()
    is_valid = serializers.SerializerMethodField()

    class Meta:
        model = Promotion
        fields = [
            'id', 'code', 'description', 'image', 'image_url', 'value_display',
            'is_active', 'value', 'is_percentage', 'min_purchase_amount',
            'usage_limit', 'usage_count', 'valid_from', 'valid_until', 'is_valid'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'usage_count', 'is_valid']

    def get_image_url(self, obj):
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
        return None

    def get_value_display(self, obj):
        return f"{obj.value}%" if obj.is_percentage else f"{obj.value}đ"

    def get_is_valid(self, obj):
        """Return the validity status of the promotion"""
        return obj.is_valid

class CartPromotionSerializer(serializers.ModelSerializer):
    """Serializer for CartPromotion model"""
    promotion = PromotionSerializer(read_only=True)
    code = serializers.CharField(write_only=True)
    
    class Meta:
        model = CartPromotion
        fields = ['id', 'promotion', 'applied_at', 'code']
        read_only_fields = ['id', 'applied_at', 'promotion']
    
    def validate_code(self, value):
        try:
            promotion = Promotion.objects.get(code=value, is_active=True)
            if not promotion.is_valid:
                raise serializers.ValidationError("This promotion code has expired or reached its usage limit.")
        except Promotion.DoesNotExist:
            raise serializers.ValidationError("Invalid or inactive promotion code.")
        return value
    
    def create(self, validated_data):
        cart = self.context['cart']
        code = validated_data.pop('code')
        promotion = Promotion.objects.get(code=code, is_active=True)
        
        # Check if this promotion is already applied to the cart
        if CartPromotion.objects.filter(cart=cart, promotion=promotion).exists():
            raise serializers.ValidationError({"code": "This promotion code is already applied to your cart."})
        
        # Check if cart total meets minimum purchase amount
        if cart.total_price < promotion.min_purchase_amount:
            raise serializers.ValidationError(
                {"code": f"Your cart total must be at least {promotion.min_purchase_amount}đ to use this promotion."}
            )
        
        return CartPromotion.objects.create(cart=cart, promotion=promotion)

class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    applied_promotions = CartPromotionSerializer(many=True, read_only=True)
    discount_amount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_price_after_discount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = Cart
        fields = ['id', 'items', 'total_price', 'applied_promotions', 
                 'discount_amount', 'total_price_after_discount', 
                 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
